package com.inin.responsemanagement.service.service;

import com.inin.responsemanagement.service.model.database.v2.DbLibraryV2;
import com.inin.responsemanagement.service.model.database.v2.DbResponse2V2;
import com.inin.responsemanagement.service.model.database.v2.DbResponseMapV2;
import com.inin.responsemanagement.service.repository.v2.LibraryRepositoryV2;
import com.inin.responsemanagement.service.repository.v2.Response2RepositoryV2;
import com.inin.responsemanagement.service.repository.v2.ResponseMapRepositoryV2;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.HeadBucketRequest;
import software.amazon.awssdk.services.s3.model.HeadBucketResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AnalyticsServiceTest {

    @Mock
    private LibraryRepositoryV2 libraryRepository;

    @Mock
    private Response2RepositoryV2 response2Repository;

    @Mock
    private ResponseMapRepositoryV2 responseMapRepository;

    @Mock
    private S3Client s3Client;

    @InjectMocks
    private AnalyticsService analyticsService;

    @BeforeEach
    public void setup() {
        ReflectionTestUtils.setField(analyticsService, "analyticsBucket", "test-bucket");
    }

    @Test
    public void testGenerateAnalytics() {
        // Setup mock data
        List<DbLibraryV2> libraries = new ArrayList<>();
        DbLibraryV2 library = new DbLibraryV2();
        library.setId("lib-123");
        library.setOrganizationId("org-123");
        library.setName("Test Library");
        library.setDateCreated("2023-01-01T00:00:00Z");
        library.setVersion(1);
        libraries.add(library);

        List<DbResponseMapV2> responseMaps = new ArrayList<>();
        DbResponseMapV2 responseMap = new DbResponseMapV2();
        responseMap.setLibraryId("lib-123");
        responseMap.setResponseId("resp-123");
        responseMap.setOrganizationId("org-123");
        responseMap.setWhatsapp(true);
        responseMaps.add(responseMap);

        DbResponse2V2 response = new DbResponse2V2();
        response.setId("resp-123.org-123");
        response.setName("Test Response");
        response.setDateCreated("2023-01-01T00:00:00Z");
        response.setVersion(1);
        response.setResponseType("Standard");

        // Setup mock behavior
        when(libraryRepository.getAllV2(anyString())).thenReturn(libraries);
        when(responseMapRepository.getLibraryMappingsV2(anyString())).thenReturn(responseMaps);
        when(response2Repository.getConsistentReadV2(anyString())).thenReturn(response);

        // Execute the method
        Map<String, Object> result = analyticsService.pushAnalyticsDataToS3("test-correlation-id");

        // Verify the result
        assertNotNull(result);
        assertEquals("success", result.get("status"));
        assertEquals(1, result.get("recordCount"));
        
        // Verify S3 uploads (3 files: data, success marker, schema)
        verify(s3Client, times(3)).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }
}