package com.inin.responsemanagement.service.controller;

import com.inin.responsemanagement.service.service.AnalyticsService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AnalyticsControllerTest {

    @Mock
    private AnalyticsService analyticsService;

    @InjectMocks
    private AnalyticsController analyticsController;

    @Test
    public void testGenerateAnalytics() {
        // Setup mock data
        Map<String, Object> serviceResult = new HashMap<>();
        serviceResult.put("status", "success");
        serviceResult.put("recordCount", 10);
        serviceResult.put("outputPath", "s3://test-bucket/test-path");

        // Setup mock behavior
        when(analyticsService.generateAnalytics(anyString())).thenReturn(serviceResult);

        // Execute the method
        ResponseEntity<Map<String, Object>> response = analyticsController.generateAnalytics();

        // Verify the result
        assertNotNull(response);
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("success", response.getBody().get("status"));
        assertEquals(10, response.getBody().get("recordCount"));
    }

    @Test
    public void testGenerateAnalyticsError() {
        // Setup mock behavior to throw an exception
        when(analyticsService.generateAnalytics(anyString())).thenThrow(new RuntimeException("Test exception"));

        // Execute the method
        ResponseEntity<Map<String, Object>> response = analyticsController.generateAnalytics();

        // Verify the result
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
    }
}