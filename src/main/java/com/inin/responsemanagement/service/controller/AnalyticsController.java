package com.inin.responsemanagement.service.controller;

import com.inin.responsemanagement.service.service.AnalyticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.UUID;

/**
 * Controller for analytics operations
 * Migrated from Python lambda function
 */
@RestController
@RequestMapping(name = "Analytics", value = "/v1/analytics/job")
@Slf4j
public class AnalyticsController extends ControllerBase {

    private final AnalyticsService analyticsService;

    @Autowired
    public AnalyticsController(AnalyticsService analyticsService) {
        this.analyticsService = analyticsService;
    }

    /**
     * Generate analytics data and upload to S3
     * This endpoint replaces the Python lambda function
     *
     * @return ResponseEntity with the result of the operation
     */
    @PostMapping("/generate")
    public ResponseEntity<Map<String, Object>> pushAnalyticsDataToS3() {
        String correlationId = UUID.randomUUID().toString();
        log.info("Received request to generate analytics with correlationId: {}", correlationId);
        
        try {
            Map<String, Object> result = analyticsService.pushAnalyticsDataToS3(correlationId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error generating analytics", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
