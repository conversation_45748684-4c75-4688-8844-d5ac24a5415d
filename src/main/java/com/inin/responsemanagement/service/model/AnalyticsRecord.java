package com.inin.responsemanagement.service.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Model class for analytics records
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalyticsRecord {
    private String organizationId;
    private String libraryId;
    private String libraryName;
    private String libraryCreatedDate;
    private Integer libraryVersion;
    private String responseId;
    private String responseType;
    private String responseCreatedDate;
    private Integer responseVersion;
    private Boolean whatsapp;
}